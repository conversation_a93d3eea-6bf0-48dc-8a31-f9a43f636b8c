name: Setup

runs:
  using: composite
  steps:
    - uses: actions/setup-node@v3
      with:
        node-version: 20

    - run: npm install -g yarn
      shell: bash

    - id: yarn-cache
      run: echo "::set-output name=dir::$(yarn cache dir)"
      shell: bash

    - uses: actions/cache@v3
      with:
        path: ${{ steps.yarn-cache.outputs.dir }}
        key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
        restore-keys: |
          ${{ runner.os }}-yarn-

    - run: yarn
      shell: bash
