name: CHECK_PACKAGE_VERSION

on:
  pull_request:
    branches: [ master ]
jobs:
  check-package-version:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'
      - run: yarn global add semver-compare-cli
      - run: export PATH="$PATH:$(yarn global bin)"
      - name: Get local package version
        run: echo "::set-output name=CURRENT_PACKAGE_VERISION::$(node -p 'require(`./package.json`).version')"
        id: current-package-version
      - name: Get remote package version
        run: echo "::set-output name=REMOTE_PACKAGE_VERISION::$(npm view @1inch/solidity-utils version)"
        id: remote-package-version
      - name: Check Version
        run: semver-compare ${{ steps.current-package-version.outputs.CURRENT_PACKAGE_VERISION }} gt ${{ steps.remote-package-version.outputs.REMOTE_PACKAGE_VERISION }}
