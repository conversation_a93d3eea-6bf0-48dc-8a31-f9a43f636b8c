name: CREATE_RELEASE

on:
  workflow_dispatch:

jobs:
  create-release:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'
      - run: yarn
      - run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Fetch tags from git history
        run: git fetch --unshallow --tags

      - name: Extract version from package.json
        id: package_version
        run: echo "VERSION=$(jq -r .version package.json)" >> $GITHUB_ENV

      - name: Check for changelog entry
        id: changelog
        run: |
          VERSION=${{ env.VERSION }}
          echo "Searching for version: $VERSION in CHANGELOG.md"

          CHANGELOG_OUTPUT=$(yarn changelog --stdout)
          NOTES=$(echo "$CHANGELOG_OUTPUT" | awk "/\/${VERSION//./\\.} \(/ {print; flag=1; next} /^solidity-utils\// {flag=0} flag")

          echo "$NOTES"

          if [ -z "$NOTES" ]; then
            echo "❌ No changelog entry found for version $VERSION. Check the version tag is exist."
            exit 1
          fi

          {
            echo "notes<<EOF"
            printf "%s\n" "$NOTES"
            echo "EOF"
          } >> "$GITHUB_OUTPUT"
        shell: bash


      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ env.VERSION }}
          name: Release ${{ env.VERSION }}
          body: ${{ steps.changelog.outputs.notes }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
