<div align="center">
    <img src="https://github.com/1inch/solidity-utils/blob/master/.github/1inch_github_w.svg#gh-light-mode-only">
    <img src="https://github.com/1inch/solidity-utils/blob/master/.github/1inch_github_b.svg#gh-dark-mode-only">
</div>

# Utility Library for Smart Contracts and Testing

[![Build Status](https://github.com/1inch/solidity-utils/workflows/CI/badge.svg)](https://github.com/1inch/solidity-utils/actions)
[![Coverage Status](https://codecov.io/gh/1inch/solidity-utils/branch/master/graph/badge.svg?token=HJWBIVXQQA)](https://codecov.io/gh/1inch/solidity-utils)
[![NPM Package](https://img.shields.io/npm/v/@1inch/solidity-utils.svg)](https://www.npmjs.org/package/@1inch/solidity-utils)

This repository is a comprehensive toolkit designed to streamline and optimize the development, tests and management of smart contracts. This repository serves as a one-stop resource for developers working on Ethereum and EVM-compatible blockchain projects, offering a blend of Solidity contracts with various utilities and optimizations, alongside JavaScript tools for testing, documentation, and project management.

## Features

- **[Development and Testing](https://github.com/1inch/solidity-utils/blob/master/docs/js/modules/src.md)**: The tools and utilities provided in this section are designed to aid in the development and testing of smart contracts. They help avoid the duplication of helper methods across different repositories by centralizing commonly used functions and scripts. This not only streamlines the development process but also ensures that the latest versions of these helpers are readily available and consistently used throughout your projects. Leveraging these utilities can significantly enhance efficiency and maintainability of your smart contract code.

- **[Configuration and Management](https://github.com/1inch/solidity-utils/blob/master/docs/js/modules/hardhat_setup.md)**: Tools for setting up Hardhat network configurations and managing project settings efficiently.

- **[Solidity Contracts and Libraries](https://github.com/1inch/solidity-utils/blob/master/docs/SUMMARY.md)**: Frequently used smart contracts, libraries, and interfaces aimed at providing reusable code for common use cases, alongside optimizations to improve contract efficiency.

- **[Documentation Templates](https://github.com/1inch/solidity-utils/blob/master/docgen/README.md)**: Templates for Documentation Generator docgen.

## Contributing

Contributions are welcome! If you have a suggestion that would make this repository better, or if you have any questions, please feel free to fork the repo and create a pull request. You can also simply open an issue with your suggestion or question.
