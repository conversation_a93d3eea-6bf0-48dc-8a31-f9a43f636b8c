// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StringUtil Gas usage Empty bytes 1`] = `22212n`;

exports[`StringUtil Gas usage Empty bytes naive 1`] = `22558n`;

exports[`StringUtil Gas usage Extremely long byte array gas 1`] = `55747n`;

exports[`StringUtil Gas usage Extremely long byte array gas naive 1`] = `531668n`;

exports[`StringUtil Gas usage Single byte 1`] = `22889n`;

exports[`StringUtil Gas usage Single byte naive 1`] = `23208n`;

exports[`StringUtil Gas usage Uint 128 1`] = `22574n`;

exports[`StringUtil Gas usage Uint 128 naive 1`] = `38061n`;

exports[`StringUtil Gas usage Uint 256 1`] = `22766n`;

exports[`StringUtil Gas usage Uint 256 as bytes 1`] = `23279n`;

exports[`StringUtil Gas usage Uint 256 as bytes naive 1`] = `38856n`;

exports[`StringUtil Gas usage Uint 256 naive 1`] = `38253n`;

exports[`StringUtil Gas usage Very long byte array gas 1`] = `25023n`;

exports[`StringUtil Gas usage Very long byte array gas naive 1`] = `56788n`;
