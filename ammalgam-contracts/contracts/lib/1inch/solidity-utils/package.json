{"name": "@1inch/solidity-utils", "version": "6.6.0", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {".": "./dist/src/index.js", "./hardhat-setup": "./dist/hardhat-setup/index.js", "./docgen": "./dist/docgen/index.js"}, "repository": {"type": "git", "url": "git+ssh://**************/1inch/solidity-utils.git"}, "license": "MIT", "scripts": {"clean": "rimraf artifacts cache coverage contracts/hardhat-dependency-compiler dist typechain-types", "build": "yarn typechain && cti src hardhat-setup docgen -b && tsc -p tsconfig.publish.json", "prepack": "yarn clean && yarn build", "coverage": "hardhat coverage", "docgen": "yarn docgen:sol && yarn docgen:ts", "docgen:ts": "npx typedoc --options typedoc.json 2>&1 | grep -v \"typechain-types\"", "docgen:sol": "yarn hardhat docgen", "format": "yarn format-ts && yarn format-sol", "format-ts": "prettier '**/*.ts' --write", "format-sol": "prettier '**/*.sol' --write", "lint": "yarn run lint:ts && yarn run lint:sol", "lint:fix": "yarn run lint:ts:fix && yarn run lint:sol:fix", "lint:ts": "eslint . --ext .ts", "lint:ts:fix": "eslint . --fix --ext .ts", "lint:sol": "solhint --max-warnings 0 \"contracts/**/*.sol\"", "lint:sol:fix": "solhint --max-warnings 0 \"contracts/**/*.sol\" --fix", "test": "hardhat test --parallel", "test:ci": "hardhat test", "test:update-snapshots": "UPDATE_SNAPSHOT=1 yarn test:ci", "test:us": "UPDATE_SNAPSHOT=1 yarn test:ci", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "typechain": "hardhat typechain", "changelog": "npx auto-changelog"}, "dependencies": {"@metamask/eth-sig-util": "8.0.0", "@nomicfoundation/hardhat-ethers": "3.0.8", "@nomicfoundation/hardhat-network-helpers": "1.0.12", "@nomicfoundation/hardhat-verify": "2.0.11", "@openzeppelin/contracts": "5.1.0", "@uniswap/permit2-sdk": "1.3.0", "chai": "4.5.0", "dotenv": "16.4.5", "ethereumjs-util": "7.1.5", "ethers": "6.13.4", "hardhat": "2.23.0", "hardhat-deploy": "0.14.0", "mocha-chai-jest-snapshot": "1.1.6", "node-fetch": "2.7.0"}, "devDependencies": {"@0xsequence/create3": "0xsequence/create3", "@nomicfoundation/hardhat-chai-matchers": "2.0.8", "@typechain/ethers-v6": "0.5.1", "@typechain/hardhat": "9.1.0", "@types/mocha": "10.0.9", "@types/node-fetch": "2.6.11", "@typescript-eslint/eslint-plugin": "8.13.0", "@typescript-eslint/parser": "8.13.0", "acquit": "1.3.0", "auto-changelog": "^2.5.0", "commander": "12.1.0", "create-ts-index": "1.14.0", "eslint": "8.57.1", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-n": "17.12.0", "eslint-plugin-promise": "7.1.0", "hardhat-gas-reporter": "2.2.1", "prettier": "3.3.3", "prettier-plugin-solidity": "1.4.1", "rimraf": "6.0.1", "solc": "0.8.28", "solhint": "5.0.3", "solidity-coverage": "0.8.13", "solidity-docgen": "0.6.0-beta.36", "ts-node": "10.9.2", "typechain": "8.3.2", "typedoc": "0.26.11", "typedoc-plugin-markdown": "4.2.10", "typescript": "5.6.3"}, "bugs": {"url": "https://github.com/1inch/solidity-utils/issues"}, "homepage": "https://github.com/1inch/solidity-utils#readme", "author": "1inch", "description": "Solidity and TS utils", "files": ["./dist", "./*.MD", "contracts/*.sol", "contracts/interfaces", "contracts/libraries", "contracts/mixins", "contracts/mocks", "docgen/templates", "changelog/*.hbs"], "auto-changelog": {"output": "CHANGELOG.md", "template": "./changelog/changelog-template.hbs", "commitLimit": 0}}