[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / ether

# Function: ether()

> **ether**(`n`): `bigint`

## Parameters

• **n**: `string`

The amount of Ether to convert, specified as a string.

## Returns

`bigint`

The equivalent amount in Wei as a bigint.

## Defined in

[src/prelude.ts:26](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/prelude.ts#L26)
