[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / signMessage

# Function: signMessage()

> **signMessage**(`signer`, `messageHex`): `Promise`\<`string`\>

## Parameters

• **signer**: `Wallet` \| `object`

Signer object or wallet instance.

• **messageHex**: `string` \| `Uint8Array` = `'0x'`

The message to sign, in hex format.

## Returns

`Promise`\<`string`\>

The signed message string.

## Defined in

[src/utils.ts:364](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/utils.ts#L364)
