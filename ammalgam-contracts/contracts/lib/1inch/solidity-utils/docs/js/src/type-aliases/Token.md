[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / Token

# Type Alias: Token

> **Token**: `object`

## Type declaration

### balanceOf()

> **balanceOf**: (`address`) => `Promise`\<`bigint`\>

#### Parameters

• **address**: `string`

#### Returns

`Promise`\<`bigint`\>

### getAddress()

> **getAddress**: () => `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

## Param

Method which retrieves the balance of the specified address.

## Param

Method which retrieves the token contract's address.

## Defined in

[src/utils.ts:292](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/utils.ts#L292)
