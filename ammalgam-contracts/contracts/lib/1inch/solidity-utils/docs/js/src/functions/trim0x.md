[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / trim0x

# Function: trim0x()

> **trim0x**(`bigNumber`): `string`

## Parameters

• **bigNumber**: `string` \| `bigint`

The number (as a bigint or string) from which to remove the '0x' prefix.

## Returns

`string`

The string without the '0x' prefix.

## Defined in

[src/permit.ts:44](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/permit.ts#L44)
