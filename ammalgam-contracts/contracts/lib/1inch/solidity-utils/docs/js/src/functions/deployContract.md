[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / deployContract

# Function: deployContract()

> **deployContract**(`name`, `parameters`): `Promise`\<`BaseContract`\>

## Parameters

• **name**: `string`

The contract name.

• **parameters**: `BigNumberish`[] = `[]`

Constructor parameters for the contract.

## Returns

`Promise`\<`BaseContract`\>

The deployed contract instance.

## Defined in

[src/utils.ts:261](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/utils.ts#L261)
