[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / constants

# Variable: constants

> `const` **constants**: `object`

## Type declaration

### DEV\_CHAINS

> `readonly` **DEV\_CHAINS**: `string`[]

### EEE\_ADDRESS

> `readonly` **EEE\_ADDRESS**: `"0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE"` = `'0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE'`

### MAX\_INT256

> `readonly` **MAX\_INT256**: `bigint`

### MAX\_UINT128

> `readonly` **MAX\_UINT128**: `bigint`

### MAX\_UINT256

> `readonly` **MAX\_UINT256**: `bigint`

### MAX\_UINT32

> `readonly` **MAX\_UINT32**: `bigint`

### MAX\_UINT48

> `readonly` **MAX\_UINT48**: `bigint`

### MIN\_INT256

> `readonly` **MIN\_INT256**: `bigint`

### ZERO\_ADDRESS

> `readonly` **ZERO\_ADDRESS**: `"0x0000000000000000000000000000000000000000"` = `'0x0000000000000000000000000000000000000000'`

### ZERO\_BYTES32

> `readonly` **ZERO\_BYTES32**: `"0x0000000000000000000000000000000000000000000000000000000000000000"` = `'0x0000000000000000000000000000000000000000000000000000000000000000'`

## Defined in

[src/prelude.ts:4](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/prelude.ts#L4)
