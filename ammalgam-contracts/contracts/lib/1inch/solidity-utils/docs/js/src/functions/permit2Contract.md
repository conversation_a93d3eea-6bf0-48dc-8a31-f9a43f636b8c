[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [src](../README.md) / permit2Contract

# Function: permit2Contract()

> **permit2Contract**(): `Promise`\<`IPermit2`\>

## Returns

`Promise`\<`IPermit2`\>

The contract instance of IPermit2.

## Defined in

[src/permit.ts:152](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/src/permit.ts#L152)
