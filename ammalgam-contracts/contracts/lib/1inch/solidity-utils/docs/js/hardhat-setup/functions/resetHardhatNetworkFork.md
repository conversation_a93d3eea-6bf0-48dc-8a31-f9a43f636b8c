[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [hardhat-setup](../README.md) / resetHardhatNetworkFork

# Function: resetHardhatNetworkFork()

> **resetHardhatNetworkFork**(`network`, `networkName`): `Promise`\<`void`\>

## Parameters

• **network**: `Network`

The Hardhat network object.

• **networkName**: `string`

The name of the network to reset to.

## Returns

`Promise`\<`void`\>

## Defined in

[hardhat-setup/networks.ts:46](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/hardhat-setup/networks.ts#L46)
