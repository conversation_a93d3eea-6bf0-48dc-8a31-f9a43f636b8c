[**@1inch/solidity-utils**](../../README.md) • **Docs**

***

[@1inch/solidity-utils](../../README.md) / [hardhat-setup](../README.md) / parseRpcEnv

# Function: parseRpcEnv()

> **parseRpcEnv**(`envRpc`): `object`

## Parameters

• **envRpc**: `string`

The RPC configuration string to parse.

## Returns

`object`

An object containing the RPC URL and optional auth key HTTP header.

### authKeyHttpHeader?

> `optional` **authKeyHttpHeader**: `string`

### url

> **url**: `string`

## Defined in

[hardhat-setup/networks.ts:32](https://github.com/1inch/solidity-utils/blob/f9426ba6dab1eac9ac07fe3976b8d1cb2d2e5ba1/hardhat-setup/networks.ts#L32)
