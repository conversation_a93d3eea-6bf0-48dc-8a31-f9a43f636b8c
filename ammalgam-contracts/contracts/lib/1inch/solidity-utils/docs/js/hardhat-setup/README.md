[**@1inch/solidity-utils**](../README.md) • **Docs**

***

[@1inch/solidity-utils](../README.md) / hardhat-setup

# hardhat-setup

## Index

### Hardhat-Setup
A helper method to get the network name from the command line arguments.

- [getNetwork](functions/getNetwork.md)

### Hardhat-Setup
A helper method to parse RPC configuration strings. Checks that the string is in the expected format.

- [parseRpcEnv](functions/parseRpcEnv.md)

### Hardhat-Setup
A helper method to reset the Hardhat network to the local network or to a fork.

- [resetHardhatNetworkFork](functions/resetHardhatNetworkFork.md)

### Hardhat-Setup
Configuration type for managing Etherscan integration in Hardhat setups.

- [Etherscan](type-aliases/Etherscan.md)

### Hardhat-Setup
The Network class is a helper class to register networks and Etherscan API keys.
See the [README](https://github.com/1inch/solidity-utils/tree/master/hardhat-setup/README.md) for usage.

- [Networks](classes/Networks.md)
