
## IERC20MetadataUppercase

_Interface for ERC20 token metadata with uppercase naming convention._

### Functions list
- [NAME() external](#name)
- [SYMBOL() external](#symbol)

### Functions
### NAME

```solidity
function NAME() external view returns (string)
```
Gets the token name.

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
[0] | string | Token name. |

### SYMBOL

```solidity
function SYMBOL() external view returns (string)
```
Gets the token symbol.

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
[0] | string | Token symbol. |

