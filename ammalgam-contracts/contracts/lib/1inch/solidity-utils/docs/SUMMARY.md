# Table of contents

* [Main Readme](README.md)
* [contracts](contracts/README.md)
	* [interfaces](contracts/interfaces/README.md)
		* [ICreate3Deployer](contracts/interfaces/ICreate3Deployer.md)
		* [IDaiLikePermit](contracts/interfaces/IDaiLikePermit.md)
		* [IERC20MetadataUppercase](contracts/interfaces/IERC20MetadataUppercase.md)
		* [IERC7597Permit](contracts/interfaces/IERC7597Permit.md)
		* [IPermit2](contracts/interfaces/IPermit2.md)
		* [IWETH](contracts/interfaces/IWETH.md)
	* [libraries](contracts/libraries/README.md)
		* [AddressArray](contracts/libraries/AddressArray.md)
		* [AddressLib](contracts/libraries/AddressLib.md)
		* [AddressSet](contracts/libraries/AddressSet.md)
		* [BySigTraits](contracts/libraries/BySigTraits.md)
		* [BytesMemory](contracts/libraries/BytesMemory.md)
		* [BytesStorage](contracts/libraries/BytesStorage.md)
		* [ECDSA](contracts/libraries/ECDSA.md)
		* [RevertReasonForwarder](contracts/libraries/RevertReasonForwarder.md)
		* [RevertReasonParser](contracts/libraries/RevertReasonParser.md)
		* [SafeERC20](contracts/libraries/SafeERC20.md)
		* [StringUtil](contracts/libraries/StringUtil.md)
		* [UniERC20](contracts/libraries/UniERC20.md)
	* [mixins](contracts/mixins/README.md)
		* [BySig](contracts/mixins/BySig.md)
		* [EthReceiver](contracts/mixins/EthReceiver.md)
		* [OnlyWethReceiver](contracts/mixins/OnlyWethReceiver.md)
		* [PermitAndCall](contracts/mixins/PermitAndCall.md)
		* [SelfdestructEthSender](contracts/mixins/SelfdestructEthSender.md)
	* [mocks](contracts/mocks/README.md)
		* [ERC20PermitMock](contracts/mocks/ERC20PermitMock.md)
		* [SelfdestructEthSenderMock](contracts/mocks/SelfdestructEthSenderMock.md)
		* [TokenCustomDecimalsMock](contracts/mocks/TokenCustomDecimalsMock.md)
		* [TokenMock](contracts/mocks/TokenMock.md)
* [js](js/README.md)
	* [classes](js/classes/README.md)
		* [hardhat_setup.Networks](js/classes/hardhat_setup.Networks.md)
	* [interfaces](js/interfaces/README.md)
		* [src.DeployContractOptions](js/interfaces/src.DeployContractOptions.md)
	* [modules](js/modules/README.md)
		* [hardhat_setup](js/modules/hardhat_setup.md)
		* [src](js/modules/src.md)
	* [modules](js/modules.md)