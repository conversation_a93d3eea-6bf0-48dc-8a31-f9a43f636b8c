{"name": "@1inch/farming", "version": "3.3.0", "description": "Set of contracts for farming incentives", "homepage": "https://github.com/1inch/farming#readme", "author": "1inch", "files": ["contracts/*.sol", "contracts/accounting", "contracts/interfaces", "/test/utils.js", "/test/behaviors/*.js"], "repository": {"type": "git", "url": "**************:1inch/farming.git"}, "license": "MIT", "dependencies": {"@1inch/token-plugins": "1.3.0", "@1inch/solidity-utils": "3.5.5", "@openzeppelin/contracts": "5.0.1"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "2.0.2", "@nomicfoundation/hardhat-ethers": "3.0.5", "@nomicfoundation/hardhat-network-helpers": "1.0.10", "@nomicfoundation/hardhat-verify": "2.0.2", "acquit": "1.3.0", "acquit-markdown": "https://github.com/vkarpov15/acquit-markdown.git#83ccd44", "chai": "4.3.10", "dotenv": "16.3.1", "eslint": "8.56.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-n": "16.4.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "5.0.0", "ethers": "6.9.0", "hardhat": "2.19.2", "hardhat-dependency-compiler": "1.1.3", "hardhat-deploy": "0.11.45", "hardhat-gas-reporter": "1.0.9", "rimraf": "5.0.5", "solhint": "3.6.2", "solidity-coverage": "0.8.5"}, "scripts": {"test": "hardhat test --parallel", "test:ci": "hardhat test", "clean": "rimraf artifacts cache coverage", "deploy": "hardhat deploy --network", "build": "rimraf ./dist && mkdir dist && cp package.json ./dist && cp README.md ./dist && cp -R ./contracts ./dist/contracts", "coverage": "hardhat coverage", "lint:js": "eslint .", "lint:js:fix": "eslint . --fix", "lint:sol": "solhint --max-warnings 0 \"contracts/**/*.sol\"", "lint:sol:fix": "solhint --max-warnings 0 \"contracts/**/*.sol\" --fix", "lint": "yarn run lint:js && yarn run lint:sol", "lint:fix": "yarn run lint:js:fix && yarn run lint:sol:fix"}}